<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>保护参数 - 桂林智源 SVG 数字化系统</title>
    <link rel="shortcut icon" href="../logo.png">
    <!-- 引入 MQTT 客户端库 -->
    <script src="https://unpkg.com/mqtt@4.3.7/dist/mqtt.min.js"></script>
    <!-- 引入通用参数配置样式 -->
    <link rel="stylesheet" href="../common/parameter-config.css">
    <!-- 保护参数专用样式 -->
    <style>
        /* 保护参数专用样式扩展 */
        .protection-param-panel {
            flex: 1;
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 15px;
            overflow: visible;
            min-width: 0;
            margin-bottom: 15px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
            position: relative;
            padding-bottom: 70px;
            overflow: hidden;
            min-height: 0;
        }

        /* 三面板布局样式 */
        .main-layout {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding-bottom: 20px;
        }

        .triple-panel {
            display: flex;
            flex-direction: column;
            min-height: auto;
        }

        /* 控件容器样式 - 统一所有控件的容器 */
        .control-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            padding: 4px 0;
        }

        /* 浮点数输入框样式 */
        .float-input {
            width: 100px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .float-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .float-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        /* 整数输入框样式 */
        .integer-input {
            width: 100px;
            height: 30px;
            background: rgba(42, 49, 66, 0.9);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            color: #ffffff;
            text-align: center;
            font-size: 14px;
            font-weight: bold;
            margin: 0 auto;
            display: block;
        }

        .integer-input:focus {
            outline: none;
            border-color: rgba(0, 212, 255, 0.6);
            box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
        }

        .integer-input:invalid {
            border-color: rgba(220, 53, 69, 0.6);
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
        }

        .protection-param-panel {
            background: rgba(26, 31, 46, 0.95);
            border: 2px solid rgba(0, 212, 255, 0.4);
            border-radius: 8px;
            padding: 10px;
            overflow: visible;
            min-width: 0;
            display: flex;
            flex-direction: column;
            min-height: auto;
        }

        /* 表格容器样式 */
        .table-container {
            flex: 1;
            overflow: visible;
            min-height: 0;
        }

        /* 面板标题优化 */
        .panel-title {
            font-size: 16px;
            color: #00d4ff;
            text-align: center;
            margin-bottom: 8px;
            padding: 6px 0;
            background: rgba(0, 212, 255, 0.1);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 4px;
            font-weight: bold;
            flex-shrink: 0;
        }

        /* 表格样式优化 - 支持每行两个参数 */
        .params-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 14px;
        }

        .params-table th {
            background: rgba(0, 212, 255, 0.3);
            color: #00d4ff;
            padding: 6px 3px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.4);
            font-weight: bold;
            font-size: 12px;
        }

        .params-table td {
            padding: 3px 2px;
            text-align: center;
            border: 1px solid rgba(0, 212, 255, 0.2);
            background: rgba(42, 49, 66, 0.7);
            vertical-align: middle;
            height: 50px;
        }

        .params-table tr:hover td {
            background: rgba(42, 49, 66, 0.9);
            border-color: rgba(0, 212, 255, 0.4);
        }

        /* 序号列样式 */
        .param-index {
            font-size: 12px;
            color: #7a8ba0;
            font-weight: bold;
            width: 30px;
            text-align: center;
        }

        /* 参数名称列样式 */
        .param-name {
            font-size: 12px;
            color: #ffffff;
            text-align: left;
            padding-left: 4px;
            line-height: 1.2;
            max-width: 120px;
            width: 120px;
            word-wrap: break-word;
            overflow: visible;
            white-space: normal;
        }

        /* 当前值列样式 */
        .param-current {
            font-size: 12px;
            color: #00d4ff;
            font-weight: bold;
            width: 80px;
            text-align: center;
        }

        /* 设定值列样式 */
        .param-setting {
            width: 110px;
            text-align: center;
            padding: 0 5px;
        }
    </style>
</head>
<body>
    <!-- MQTT 连接状态指示器 -->
    <div class="mqtt-status-container">
        <div class="mqtt-connection-status disconnected" id="mqtt-status">MQTT 连接中...</div>
        <div class="data-timestamp" id="data-timestamp">等待数据...</div>
    </div>

    <div class="container">
        <div class="header">
            <h1>保护参数</h1>
        </div>

        <div class="main-content">
            <div class="main-layout">
                <!-- 第一面板：线电压保护 -->
                <div class="triple-panel">
                    <div class="protection-param-panel">
                        <div class="panel-title">线电压保护</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="voltage-protection-table">
                                    <!-- 线电压保护参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 第二面板：输出电流保护 -->
                <div class="triple-panel">
                    <div class="protection-param-panel">
                        <div class="panel-title">输出电流保护</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="current-protection-table">
                                    <!-- 输出电流保护参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- 第三面板：其他保护 -->
                <div class="triple-panel">
                    <div class="protection-param-panel">
                        <div class="panel-title">其他保护</div>
                        <div class="table-container">
                            <table class="params-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                        <th>序号</th>
                                        <th>参数名称</th>
                                        <th>设定值</th>
                                        <th>当前值</th>
                                    </tr>
                                </thead>
                                <tbody id="other-protection-table">
                                    <!-- 其他保护参数将在这里动态生成 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button class="send-button" id="send-settings-btn" onclick="handleSendParameterSettings()">下载</button>

        <!-- 状态提示消息 -->
        <div id="status-message" class="status-message"></div>
    </div>

    <!-- 引入通用参数配置脚本 -->
    <script src="../common/parameter-config.js"></script>
    <script>
        // 确保全局变量可访问
        window.parameterManager = null;

        /**
         * 保护参数页面配置
         * 定义保护参数列表和页面设置
         */

        // 线电压保护参数组 (17个参数)
        const voltageProtectionGroup = [
            { mqttId: 'SVG_2001', name: '线电压有效值Ⅰ段过压报警值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2002', name: '线电压有效值Ⅰ段过压报警时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2003', name: '线电压有效值Ⅱ段过压保护值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2004', name: '线电压有效值Ⅱ段过压保护时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2005', name: '线电压幅值Ⅲ段过压保护值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2006', name: '线电压幅值Ⅲ段过压保护时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2007', name: '线电压瞬时值过压保护值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2008', name: '线电压有效值Ⅰ段欠压报警值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2009', name: '线电压有效值Ⅰ段欠压报警时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2010', name: '线电压有效值Ⅱ段欠压保护值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2011', name: '线电压有效值Ⅱ段欠压保护时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2012', name: '线电压幅值Ⅲ段欠压保护值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2013', name: '线电压幅值Ⅲ段欠压保护时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2014', name: '线电压幅度不平衡度保护值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2015', name: '线电压幅度不平衡保护时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2016', name: '线电压缺相保护值', type: 'float', range: { min: -99999.00, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2017', name: '线电压缺相保护时间', type: 'integer', range: { min: 0, max: 999999 } }
        ];

        // 输出电流保护参数组 (13个参数)
        const currentProtectionGroup = [
            { mqttId: 'SVG_2019', name: '输出电流有效值Ⅰ段过流报警值', type: 'float', range: { min: 0, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2020', name: '输出电流有效值Ⅰ段过流报警时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2021', name: '输出电流有效值Ⅱ段过流保护值', type: 'float', range: { min: 0, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2022', name: '输出电流有效值Ⅱ段过流保护时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2023', name: '输出电流瞬时值过流封脉冲保护值', type: 'float', range: { min: 0, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2024', name: '输出电流缺相保护值', type: 'float', range: { min: 0, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2025', name: '输出电流缺相保护时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2028', name: '输出电流瞬时值过流跳闸保护值', type: 'float', range: { min: 0, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2029', name: '输出电流瞬时值过流跳闸时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2033', name: '输出电流(CT采样)瞬时值过流跳闸保护值', type: 'float', range: { min: 0, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2034', name: '输出电流(CT采样)瞬时值过流跳闸时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2035', name: '零序电流瞬时值过流保护定值', type: 'float', range: { min: 0, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2036', name: '零序电流过流保护时间', type: 'integer', range: { min: 0, max: 999999 } }
        ];

        // 其他保护参数组 (6个参数)
        const otherProtectionGroup = [
            { mqttId: 'SVG_2018', name: '故障重新启动等待时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2030', name: '连续故障次数', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2031', name: '故障复位超时时间', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2032', name: '霍尔传感器故障保护定值', type: 'float', range: { min: 0, max: 99999.00 }, precision: 2 },
            { mqttId: 'SVG_2026', name: '单元电压过压保护值', type: 'integer', range: { min: 0, max: 999999 } },
            { mqttId: 'SVG_2027', name: '单元电压不平衡保护值', type: 'integer', range: { min: 0, max: 999999 } }
        ];

        // 合并所有参数
        const protectionParametersGroup = [...voltageProtectionGroup, ...currentProtectionGroup, ...otherProtectionGroup];

        // 保护参数配置对象
        const protectionParamConfig = {
            parameters: protectionParametersGroup,
            pageTitle: '保护参数',
            panelTitles: ['线电压保护', '输出电流保护', '其他保护'],
            parametersPerPanel: protectionParametersGroup.length,
            parametersPerRow: 2,
            tableConfig: {
                voltageTable: {
                    containerId: 'voltage-protection-table',
                    parameters: voltageProtectionGroup,
                    title: '线电压保护'
                },
                currentTable: {
                    containerId: 'current-protection-table',
                    parameters: currentProtectionGroup,
                    title: '输出电流保护'
                },
                otherTable: {
                    containerId: 'other-protection-table',
                    parameters: otherProtectionGroup,
                    title: '其他保护'
                }
            }
        };

        /**
         * 保护参数管理器类
         * 扩展通用参数配置管理器，支持浮点数和整数混合输入
         */
        class ProtectionParameterManager extends ParameterConfigManager {
            constructor(config) {
                // 必须先调用super，但传入一个最小配置避免父类的初始化
                super({ parameters: [] });
                
                // 重新设置我们的配置
                this.config = config;
                this.parameters = [];
                this.currentValues = {};
                this.modifiedValues = {};
                this.tableConfig = config.tableConfig;
                
                // 调用我们自定义的渲染方法
                this.renderParameterTable();
            }

            /**
             * 渲染参数表格
             * 重写以支持三个分组表格的布局
             */
            renderParameterTable() {
                console.log('开始渲染保护参数表格...');

                // 渲染线电压保护表格
                this.renderSingleTable(this.tableConfig.voltageTable);

                // 渲染输出电流保护表格
                this.renderSingleTable(this.tableConfig.currentTable);

                // 渲染其他保护表格
                this.renderSingleTable(this.tableConfig.otherTable);

                console.log('保护参数表格渲染完成');
            }

            /**
             * 渲染单个表格
             * @param {Object} tableConfig - 表格配置
             */
            renderSingleTable(tableConfig) {
                const tableBody = document.getElementById(tableConfig.containerId);
                if (!tableBody) {
                    console.error(`表格容器 ${tableConfig.containerId} 未找到`);
                    return;
                }

                tableBody.innerHTML = '';

                // 每行显示2个参数
                for (let i = 0; i < tableConfig.parameters.length; i += 2) {
                    const param1 = tableConfig.parameters[i];
                    const param2 = tableConfig.parameters[i + 1];
                    
                    const row = document.createElement('tr');
                    
                    // 第一个参数
                    const paramId1 = `param_${param1.mqttId}`;
                    const inputHtml1 = this.createInputHtml(param1, paramId1);
                    
                    let rowHtml = `
                        <td class="param-index">${i + 1}</td>
                        <td class="param-name">${param1.name}</td>
                        <td class="param-setting">
                            <div class="control-container">
                                ${inputHtml1}
                            </div>
                        </td>
                        <td class="param-current" id="current-${paramId1}">${this.getDefaultValue(param1)}</td>
                    `;
                    
                    // 第二个参数（如果存在）
                    if (param2) {
                        const paramId2 = `param_${param2.mqttId}`;
                        const inputHtml2 = this.createInputHtml(param2, paramId2);
                        
                        rowHtml += `
                            <td class="param-index">${i + 2}</td>
                            <td class="param-name">${param2.name}</td>
                            <td class="param-setting">
                                <div class="control-container">
                                    ${inputHtml2}
                                </div>
                            </td>
                            <td class="param-current" id="current-${paramId2}">${this.getDefaultValue(param2)}</td>
                        `;
                        
                        // 初始化第二个参数对象
                        const parameter2 = {
                            id: paramId2,
                            mqttId: param2.mqttId,
                            name: param2.name,
                            type: param2.type,
                            range: param2.range,
                            precision: param2.precision,
                            currentValue: param2.type === 'float' ? 0.00 : 0,
                            settingValue: param2.type === 'float' ? 0.00 : 0,
                            isInitialized: false
                        };
                        this.parameters.push(parameter2);
                    } else {
                        // 如果没有第二个参数，填充空列
                        rowHtml += `
                            <td class="param-index"></td>
                            <td class="param-name"></td>
                            <td class="param-setting"></td>
                            <td class="param-current"></td>
                        `;
                    }
                    
                    row.innerHTML = rowHtml;
                    tableBody.appendChild(row);
                    
                    // 初始化第一个参数对象
                    const parameter1 = {
                        id: paramId1,
                        mqttId: param1.mqttId,
                        name: param1.name,
                        type: param1.type,
                        range: param1.range,
                        precision: param1.precision,
                        currentValue: param1.type === 'float' ? 0.00 : 0,
                        settingValue: param1.type === 'float' ? 0.00 : 0,
                        isInitialized: false
                    };
                    this.parameters.push(parameter1);
                }
            }

            /**
             * 创建输入框HTML
             * @param {Object} param - 参数配置
             * @param {string} paramId - 参数ID
             * @returns {string} 输入框HTML
             */
            createInputHtml(param, paramId) {
                if (param.type === 'float') {
                    const step = param.precision === 2 ? '0.01' : '0.0001';
                    return `
                        <input type="number" 
                               class="float-input" 
                               id="input-${paramId}" 
                               step="${step}" 
                               min="${param.range.min}" 
                               max="${param.range.max}" 
                               value="${this.getDefaultValue(param)}"
                               onchange="window.parameterManager.updateFloatParam('${paramId}', this)"
                               onblur="window.parameterManager.validateFloatParam('${paramId}', this)">
                    `;
                } else {
                    return `
                        <input type="number" 
                               class="integer-input" 
                               id="input-${paramId}" 
                               step="1" 
                               min="${param.range.min}" 
                               max="${param.range.max}" 
                               value="${this.getDefaultValue(param)}"
                               onchange="window.parameterManager.updateIntegerParam('${paramId}', this)"
                               onblur="window.parameterManager.validateIntegerParam('${paramId}', this)">
                    `;
                }
            }

            /**
             * 获取默认值
             * @param {Object} param - 参数配置
             * @returns {string} 默认值
             */
            getDefaultValue(param) {
                if (param.type === 'float') {
                    return param.precision === 2 ? '0.00' : '0.0000';
                } else {
                    return '0';
                }
            }

            /**
             * 更新浮点数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            updateFloatParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const numValue = parseFloat(inputElement.value);
                const min = param.range.min;
                const max = param.range.max;
                const precision = param.precision || 2;

                if (isNaN(numValue)) {
                    // 无效输入，恢复为当前设定值
                    inputElement.value = param.settingValue.toFixed(precision);
                    showStatusMessage('请输入有效的数值', 'warning');
                    return;
                }

                if (numValue < min || numValue > max) {
                    // 超出范围，限制到边界值
                    const boundaryValue = numValue < min ? min : max;
                    param.settingValue = boundaryValue;
                    inputElement.value = boundaryValue.toFixed(precision);

                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为 ${boundaryValue.toFixed(precision)}`, 'warning');
                } else {
                    // 有效值
                    param.settingValue = parseFloat(numValue.toFixed(precision));
                }

                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 更新整数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            updateIntegerParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const numValue = parseInt(inputElement.value);
                const min = param.range.min;
                const max = param.range.max;

                if (isNaN(numValue)) {
                    // 无效输入，恢复为当前设定值
                    inputElement.value = param.settingValue.toString();
                    showStatusMessage('请输入有效的整数', 'warning');
                    return;
                }

                if (numValue < min || numValue > max) {
                    // 超出范围，限制到边界值
                    const boundaryValue = numValue < min ? min : max;
                    param.settingValue = boundaryValue;
                    inputElement.value = boundaryValue.toString();

                    showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已设置为 ${boundaryValue}`, 'warning');
                } else {
                    // 有效值
                    param.settingValue = numValue;
                }

                this.updateHighlightStatus(paramId);
                this.modifiedValues[paramId] = param.settingValue;

                console.log(`参数 ${param.name} 设定值已修改为: ${param.settingValue}`);
            }

            /**
             * 验证浮点数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            validateFloatParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const value = parseFloat(inputElement.value);
                const min = param.range.min;
                const max = param.range.max;
                const precision = param.precision || 2;

                if (isNaN(value) || value < min || value > max) {
                    // 使用当前设定值或默认值
                    const defaultValue = param.settingValue || 0.00;
                    param.settingValue = defaultValue;
                    inputElement.value = defaultValue.toFixed(precision);

                    if (!isNaN(value) && (value < min || value > max)) {
                        showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已恢复为 ${defaultValue.toFixed(precision)}`, 'warning');
                    }

                    // 更新高亮状态
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                }
            }

            /**
             * 验证整数参数
             * @param {string} paramId - 参数ID
             * @param {HTMLElement} inputElement - 输入框元素
             */
            validateIntegerParam(paramId, inputElement) {
                const param = this.parameters.find(p => p.id === paramId);
                if (!param) return;

                const value = parseInt(inputElement.value);
                const min = param.range.min;
                const max = param.range.max;

                if (isNaN(value) || value < min || value > max) {
                    // 使用当前设定值或默认值
                    const defaultValue = param.settingValue || 0;
                    param.settingValue = defaultValue;
                    inputElement.value = defaultValue.toString();

                    if (!isNaN(value) && (value < min || value > max)) {
                        showStatusMessage(`参数值必须在 ${min} 到 ${max} 之间，已恢复为 ${defaultValue}`, 'warning');
                    }

                    // 更新高亮状态
                    this.updateHighlightStatus(paramId);
                    this.modifiedValues[paramId] = param.settingValue;
                }
            }

            /**
             * 更新参数当前值（从 MQTT 数据）
             * 重写以支持浮点数和整数格式化
             */
            updateCurrentValueFromMQTT(mqttId, value) {
                const param = this.parameters.find(p => p.mqttId === mqttId);
                if (!param) return;

                // 更新参数当前值
                if (param.type === 'float') {
                    param.currentValue = parseFloat(value) || 0.00;
                } else {
                    param.currentValue = parseInt(value) || 0;
                }

                // 首次数据同步：如果参数未初始化，将设定值设为当前值
                if (!param.isInitialized) {
                    param.settingValue = param.currentValue;
                    param.isInitialized = true;

                    // 更新输入框界面
                    const inputElement = document.getElementById(`input-${param.id}`);
                    if (inputElement) {
                        if (param.type === 'float') {
                            const precision = param.precision || 2;
                            inputElement.value = parseFloat(param.settingValue).toFixed(precision);
                        } else {
                            inputElement.value = param.settingValue.toString();
                        }
                    }

                    // 记录修改
                    this.modifiedValues[param.id] = param.settingValue;

                    console.log(`首次同步参数 ${param.name}: 当前值=${param.currentValue}, 设定值=${param.settingValue} (已初始化)`);
                }

                // 更新界面显示（当前值）
                const currentElement = document.getElementById(`current-${param.id}`);
                if (currentElement) {
                    if (param.type === 'float') {
                        const precision = param.precision || 2;
                        currentElement.textContent = param.currentValue.toFixed(precision);
                    } else {
                        currentElement.textContent = param.currentValue.toString();
                    }
                    currentElement.style.color = '#00d4ff';
                }

                // 更新高亮状态
                this.updateHighlightStatus(param.id);
            }
        }

        // 全局保护参数管理器变量
        let protectionParamManager = null;

        /**
         * 检查参数管理器状态
         * @returns {boolean} 参数管理器是否已正确初始化
         */
        function checkParameterManagerStatus() {
            console.log('检查参数管理器状态...');
            console.log('protectionParamManager:', protectionParamManager);
            console.log('window.parameterManager:', window.parameterManager);

            if (!protectionParamManager) {
                console.error('protectionParamManager 未初始化');
                return false;
            }

            if (!window.parameterManager) {
                console.error('window.parameterManager 未设置');
                return false;
            }

            console.log('参数管理器状态检查通过');
            return true;
        }

        /**
         * 处理参数设置发送（保护参数专用）
         */
        async function handleSendParameterSettings() {
            console.log('开始发送保护参数设置...');

            // 检查参数管理器状态
            if (!checkParameterManagerStatus()) {
                showStatusMessage('参数管理器未正确初始化，请刷新页面重试', 'error');
                return;
            }

            // 检查 MQTT 连接状态
            if (!mqttParameterManager) {
                showStatusMessage('MQTT 管理器未初始化', 'error');
                return;
            }

            const connectionStatus = mqttParameterManager.getConnectionStatus();
            if (!connectionStatus.isConnected) {
                showStatusMessage('MQTT 未连接，无法发送参数设置', 'error');
                return;
            }

            // 获取需要发送的参数
            const modifiedParams = protectionParamManager.getModifiedParameters();

            if (modifiedParams.length === 0) {
                showStatusMessage('没有需要更新的参数（所有参数的设定值与当前值一致）', 'warning');
                return;
            }

            // 禁用发送按钮
            const sendButton = document.getElementById('send-settings-btn');
            if (sendButton) {
                sendButton.disabled = true;
                sendButton.textContent = '发送中...';
            }

            try {
                // 获取 MQTT 格式的参数数组
                const mqttParams = protectionParamManager.getMQTTParameterArray();

                console.log('准备发送的保护参数:', mqttParams);

                // 发送参数设置
                const result = await mqttParameterManager.sendParameterSettings(mqttParams);

                showStatusMessage(
                    `保护参数设置发送成功！\n发送了 ${result.parameterCount} 个参数\n时间: ${result.timestamp.toLocaleString()}`,
                    'success'
                );

                console.log('保护参数设置发送成功:', result);

            } catch (error) {
                console.error('发送保护参数设置失败:', error);
                showStatusMessage(`发送失败: ${error.message}`, 'error');
            } finally {
                // 恢复发送按钮
                if (sendButton) {
                    sendButton.disabled = false;
                    sendButton.textContent = '下载';
                }
            }
        }

        /**
         * 初始化保护参数配置页面
         * @param {Object} config - 保护参数配置对象
         */
        function initProtectionParameterConfigPage(config) {
            console.log('保护参数配置页面初始化...');

            try {
                // 初始化保护参数管理器
                protectionParamManager = new ProtectionParameterManager(config);

                // 设置全局变量以兼容通用脚本
                window.parameterManager = protectionParamManager;

                // 创建备用的全局发送函数
                window.sendParameterSettings = handleSendParameterSettings;

                console.log('保护参数管理器初始化成功:', protectionParamManager);
                console.log('全局参数管理器设置成功:', window.parameterManager);

                // 初始化 MQTT 连接
                initMQTTConnection();

                // 定期更新连接状态显示和按钮状态
                setInterval(() => {
                    if (mqttParameterManager) {
                        const status = mqttParameterManager.getConnectionStatus();
                        if (status.isConnected) {
                            updateMQTTStatus('connected', 'MQTT 已连接');
                        } else {
                            updateMQTTStatus('disconnected', `MQTT 未连接 (重试: ${status.reconnectAttempts})`);
                        }
                        // 更新发送按钮状态
                        updateSendButtonStatus();
                    }
                }, 1000);

                console.log('保护参数配置页面初始化完成');

            } catch (error) {
                console.error('保护参数配置页面初始化失败:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        }

        /**
         * 页面初始化
         */
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM 内容加载完成，开始初始化保护参数配置页面...');

            // 检查必要的依赖
            if (typeof ParameterConfigManager === 'undefined') {
                console.error('ParameterConfigManager 类未定义，请检查通用脚本是否正确加载');
                showStatusMessage('页面初始化失败：缺少必要的依赖脚本', 'error');
                return;
            }

            console.log('依赖检查通过，开始初始化保护参数管理器...');
            console.log('保护参数配置:', protectionParamConfig);

            // 使用自定义的保护参数配置管理器初始化页面
            try {
                initProtectionParameterConfigPage(protectionParamConfig);

                // 延迟检查初始化状态
                setTimeout(() => {
                    if (checkParameterManagerStatus()) {
                        console.log('保护参数管理器初始化验证成功');
                        showStatusMessage('页面初始化完成', 'success');
                    } else {
                        console.error('保护参数管理器初始化验证失败');
                        showStatusMessage('参数管理器初始化异常，请刷新页面', 'error');
                    }
                }, 1000);

            } catch (error) {
                console.error('页面初始化过程中发生错误:', error);
                showStatusMessage('页面初始化失败: ' + error.message, 'error');
            }
        });
    </script>
</body>
</html>